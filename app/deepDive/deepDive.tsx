// app/deepDive/deepDive.tsx

"use client";

import * as React from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { deepDiveContent, DeepDiveLesson, DeepDiveTable } from "./content";

const INK = "#1a1a18";
const GREY_50 = "rgba(26, 26, 24, 0.04)";
const GREY_100 = "rgba(26, 26, 24, 0.06)";
const GREY_200 = "rgba(26, 26, 24, 0.08)";
const GREY_300 = "rgba(26, 26, 24, 0.12)";
const GREY_500 = "rgba(26, 26, 24, 0.50)";
const GREY_700 = "rgba(26, 26, 24, 0.70)";
const GREY_800 = "rgba(26, 26, 24, 0.80)";

/** ========= Emphasis Utilities (Bold / Italic from content.ts) ========= */

/** Strip Arabic diacritics/combining marks for matching, preserve original for display */
function stripArabicDiacritics(input: string): string {
  // Ranges: 0610–061A, 064B–065F, 06D6–06DC, 06DF–06E8, 06EA–06ED
  return input.replace(
    /[\u0610-\u061A\u064B-\u065F\u06D6-\u06DC\u06DF-\u06E8\u06EA-\u06ED]/g,
    ""
  );
}

function escapeRegex(s: string): string {
  return s.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
}

type EmphasisOptionsRuntime = {
  wholeWord: boolean;
  caseSensitive: boolean;
  respectDiacritics: boolean;
};

type EmphasisRuntime = {
  options: EmphasisOptionsRuntime;
  /** For quick membership tests (already normalized if needed + lowercased if case-insensitive) */
  boldSet: Set<string>;
  italicSet: Set<string>;
  /** Union regex for all terms (built on normalized text if needed) */
  unionRegex: RegExp | null;
  /** Helper to normalize input for matching per options */
  normalizeForMatch: (s: string) => string;
};

/** Build a runtime matcher from section.emphasis */
function buildEmphasisRuntime(
  emphasis:
    | {
        bold?: string[];
        italic?: string[];
        options?: {
          wholeWord?: boolean;
          caseSensitive?: boolean;
          respectDiacritics?: boolean;
        };
      }
    | undefined
): EmphasisRuntime | null {
  if (!emphasis || (!emphasis.bold?.length && !emphasis.italic?.length)) {
    return null;
  }

  const opts: EmphasisOptionsRuntime = {
    wholeWord: emphasis.options?.wholeWord ?? true,
    caseSensitive: emphasis.options?.caseSensitive ?? false,
    respectDiacritics: emphasis.options?.respectDiacritics ?? false,
  };

  const normalizeForMatch = (s: string) => {
    let out = opts.respectDiacritics ? s : stripArabicDiacritics(s);
    if (!opts.caseSensitive) out = out.toLowerCase();
    return out;
  };

  const rawTerms = [
    ...(emphasis.bold ?? []),
    ...(emphasis.italic ?? []),
  ].filter(Boolean);

  if (rawTerms.length === 0) return null;

  // Sort by length desc to prefer longer matches on overlaps
  const uniqueTerms = Array.from(new Set(rawTerms)).sort(
    (a, b) => b.length - a.length
  );

  const normalizedTerms = uniqueTerms.map((t) => normalizeForMatch(t));
  const boldSet = new Set((emphasis.bold ?? []).map(normalizeForMatch));
  const italicSet = new Set((emphasis.italic ?? []).map(normalizeForMatch));

  // Build union regex
  // Word boundaries: use Unicode properties; treat letters/marks/numbers/underscore as "word"
  // Note: lookbehind supported in modern browsers; if not, fallback still gracefully avoids crashes
  const boundary = "[\\p{L}\\p{M}\\p{N}_]";
  const left = `(?<!${boundary})`;
  const right = `(?!${boundary})`;

  const unionBody = normalizedTerms.map((t) => escapeRegex(t)).join("|");

  const source = opts.wholeWord
    ? `${left}(?:${unionBody})${right}`
    : `(?:${unionBody})`;
  // Flags: u for Unicode, g for global, i for case-insensitive if needed
  const flags = `g${opts.caseSensitive ? "" : "i"}u`;

  let unionRegex: RegExp | null = null;
  try {
    unionRegex = new RegExp(source, flags as any);
  } catch {
    // Fallback without lookbehind if environment doesn’t support it
    const fallbackSource = `(?:${unionBody})`;
    unionRegex = new RegExp(fallbackSource, flags.replace("u", "") as any);
  }

  return {
    options: opts,
    boldSet,
    italicSet,
    unionRegex,
    normalizeForMatch,
  };
}

/** Render text with <strong>/<em> spans applied per runtime rules */
function renderWithEmphasis(
  text: string,
  runtime: EmphasisRuntime | null
): React.ReactNode {
  if (!runtime || !runtime.unionRegex || !text) return text;

  const { unionRegex, normalizeForMatch, boldSet, italicSet } = runtime;

  // Build normalized shadow text + index map to original positions
  const orig = text;
  const normChars: string[] = [];
  const normToOrigIdx: number[] = [];
  for (let i = 0; i < orig.length; i++) {
    const ch = orig[i];
    const keep = runtime.options.respectDiacritics
      ? ch
      : ch.replace(
          /[\u0610-\u061A\u064B-\u065F\u06D6-\u06DC\u06DF-\u06E8\u06EA-\u06ED]/g,
          ""
        );
    if (keep.length > 0) {
      // most often keep is exactly one char
      for (let k = 0; k < keep.length; k++) {
        normChars.push(keep[k]);
        normToOrigIdx.push(i);
      }
    } else {
      // diacritic-only char was dropped → no entry
    }
  }
  let normalized = normChars.join("");
  if (!runtime.options.caseSensitive) normalized = normalized.toLowerCase();

  // Find matches in normalized, map back to original indices
  const matches: Array<{
    start: number; // original start index
    end: number; // original end index (exclusive)
    kind: "bold" | "italic" | "both";
    matchedNorm: string;
  }> = [];

  if (!normalized) return text;

  unionRegex.lastIndex = 0;
  let m: RegExpExecArray | null;
  const consumed: boolean[] = new Array(normalized.length).fill(false);

  while ((m = unionRegex.exec(normalized))) {
    const nStart = m.index;
    const nEnd = nStart + m[0].length; // exclusive

    // Check overlap in normalized domain
    let overlaps = false;
    for (let i = nStart; i < nEnd; i++) {
      if (consumed[i]) {
        overlaps = true;
        break;
      }
    }
    if (overlaps) continue;

    // Determine classification by matched normalized token
    const token = normalizeForMatch(m[0]);
    const inBold = boldSet.has(token);
    const inItalic = italicSet.has(token);
    const kind: "bold" | "italic" | "both" =
      inBold && inItalic ? "both" : inBold ? "bold" : "italic";

    // Map back to original indices
    const oStart = normToOrigIdx[nStart];
    const oEnd = normToOrigIdx[Math.max(nEnd - 1, nStart)] + 1;

    matches.push({
      start: oStart,
      end: oEnd,
      kind,
      matchedNorm: token,
    });

    // Mark consumed in normalized space
    for (let i = nStart; i < nEnd; i++) consumed[i] = true;
  }

  if (matches.length === 0) return text;

  // Sort by start asc; already non-overlapping due to consumption
  matches.sort((a, b) => a.start - b.start);

  // Stitch segments
  const out: React.ReactNode[] = [];
  let cursor = 0;
  matches.forEach((seg, idx) => {
    if (cursor < seg.start) {
      out.push(orig.slice(cursor, seg.start));
    }
    const chunk = orig.slice(seg.start, seg.end);
    if (seg.kind === "both") {
      out.push(
        <strong key={`emph-${idx}-b`}>
          <em>{chunk}</em>
        </strong>
      );
    } else if (seg.kind === "bold") {
      out.push(<strong key={`emph-${idx}-b`}>{chunk}</strong>);
    } else {
      out.push(<em key={`emph-${idx}-i`}>{chunk}</em>);
    }
    cursor = seg.end;
  });
  if (cursor < orig.length) out.push(orig.slice(cursor));

  return out;
}

/** Filled right-pointing triangle (animated when selected/hovered) */
function FilledTriangle({
  size = 12,
  color = INK,
  className,
  selected = false,
}: {
  size?: number;
  color?: string;
  className?: string;
  selected?: boolean;
}) {
  return (
    <motion.svg
      viewBox="0 0 24 24"
      width={size}
      height={size}
      className={className}
      aria-hidden
      focusable="false"
      initial={false}
      animate={{
        x: selected ? 2 : 0,
        rotate: selected ? 8 : 0,
      }}
      transition={{ duration: 0.16, ease: "easeOut" }}
    >
      <polygon points="6,4 20,12 6,20" style={{ fill: color }} />
    </motion.svg>
  );
}

type DeepDiveProps = {
  lessonNumber?: number;
  className?: string;
  initialSectionKey?: string;
};

export default function DeepDive({
  lessonNumber = 1,
  className,
  initialSectionKey,
}: DeepDiveProps) {
  const lesson: DeepDiveLesson | undefined = deepDiveContent[lessonNumber];

  const [activeKey, setActiveKey] = React.useState<string | null>(
    initialSectionKey ?? null
  );

  React.useEffect(() => {
    setActiveKey(initialSectionKey ?? null);
  }, [lessonNumber, initialSectionKey]);

  const buttonRefs = React.useRef<Array<HTMLButtonElement | null>>([]);

  if (!lesson) {
    return (
      <section
        className={[
          "w-full h-full overflow-y-auto bg-transparent p-6 md:p-8",
          className || "",
        ].join(" ")}
      >
        <div className="flex items-start gap-6">
          <HeaderBadge number={lessonNumber} />
          <p style={{ color: GREY_500 }}>No deep dive content found.</p>
        </div>
      </section>
    );
  }

  const isFocused = Boolean(activeKey);
  const activeSection =
    lesson.sections.find((s) => s.key === activeKey) ?? null;

  // Build emphasis runtime for the active section (memoized)
  const emphasisRuntime = React.useMemo(
    () => buildEmphasisRuntime(activeSection?.emphasis),
    [activeSection?.emphasis]
  );

  const dockTransition = {
    duration: 0.26,
    ease: [0.34, 0.08, 0.22, 1],
  };

  const contentTransition = {
    duration: 0.2,
    delay: 0.06,
    ease: "easeOut",
  };

  const onKeyDownRail = (e: React.KeyboardEvent<HTMLDivElement>) => {
    const count = lesson.sections.length;
    if (count === 0) return;

    const indexFromKey = lesson.sections.findIndex((s) => s.key === activeKey);
    const currentIndex =
      indexFromKey === -1 ? Math.max(0, Math.min(count - 1, 0)) : indexFromKey;

    const focusAt = (i: number) => {
      const node = buttonRefs.current[i];
      if (node) node.focus();
    };

    switch (e.key) {
      case "ArrowDown":
      case "j": {
        e.preventDefault();
        const next = (currentIndex + 1) % count;
        focusAt(next);
        break;
      }
      case "ArrowUp":
      case "k": {
        e.preventDefault();
        const prev = (currentIndex - 1 + count) % count;
        focusAt(prev);
        break;
      }
      case "Home": {
        e.preventDefault();
        focusAt(0);
        break;
      }
      case "End": {
        e.preventDefault();
        focusAt(count - 1);
        break;
      }
      case "Enter":
      case " ": {
        const i = buttonRefs.current.findIndex(
          (n) => n === document.activeElement
        );
        if (i >= 0) {
          const key = lesson.sections[i].key;
          setActiveKey((p) => (p === key ? null : key));
        }
        break;
      }
      default:
        break;
    }
  };

  const listVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
      },
    },
  };

  return (
    <section
      className={[
        "w-full h-full overflow-y-auto bg-transparent p-6 md:p-10",
        className || "",
      ].join(" ")}
      aria-labelledby={`deep-dive-lesson-${lesson.lessonNumber}`}
    >
      <div
        className={`grid items-start transition-all duration-300`}
        style={{
          gap: isFocused ? "48px" : "24px",
          gridTemplateColumns: isFocused ? "260px 1fr" : "1fr",
        }}
      >
        <div
          className={`sticky top-10 h-[calc(100vh-80px)] flex flex-col`}
          style={{ display: isFocused ? "flex" : "block" }}
        >
          <motion.aside
            animate={{
              scale: isFocused ? 0.92 : 1,
              x: 0,
              y: 0,
            }}
            transition={dockTransition}
            style={{
              transformOrigin: "top left",
              width: isFocused ? 260 : "100%",
              maxWidth: isFocused ? 260 : "100%",
            }}
            className="mx-auto md:mx-0"
            role="navigation"
            aria-label="Dive Deeper topics"
            onKeyDown={onKeyDownRail}
          >
            <div className="flex items-start gap-4">
              <HeaderBadge number={lesson.lessonNumber} compact={isFocused} />
            </div>

            <nav
              aria-label="Deep Dive Sections"
              className={`${isFocused ? "mt-6" : "mt-10"} pl-6 relative`}
            >
              <span
                aria-hidden
                className="absolute left-0 top-1 bottom-1 w-[1.5px]"
                style={{ backgroundColor: GREY_200 }}
              />
              <motion.ul
                className={isFocused ? "space-y-3" : "space-y-5"}
                role="tablist"
                aria-orientation="vertical"
                variants={listVariants}
                initial="hidden"
                animate="visible"
              >
                {lesson.sections.map((section, i) => {
                  const selected = activeKey === section.key;
                  const controlId = `deep-dive-panel-${section.key}`;
                  return (
                    <motion.li
                      key={section.key}
                      className="list-none"
                      role="presentation"
                      variants={itemVariants}
                    >
                      <button
                        type="button"
                        role="tab"
                        ref={(el) => {
                          buttonRefs.current[i] = el;
                        }}
                        onClick={() =>
                          setActiveKey((p) =>
                            p === section.key ? null : section.key
                          )
                        }
                        aria-current={selected ? "true" : undefined}
                        aria-selected={selected}
                        aria-controls={controlId}
                        className={`group inline-flex items-center gap-2.5 font-medium leading-snug
                                   hover:opacity-70 focus:outline-none transition-all duration-150
                                   focus-visible:ring-2 focus-visible:ring-offset-2 rounded px-1 py-0.5`}
                        style={{
                          fontSize: isFocused ? "14px" : "20px",
                          letterSpacing: "-0.01em",
                          color: selected ? INK : GREY_800,
                          ["--tw-ring-color" as any]: GREY_300,
                          fontWeight: selected ? 600 : 500,
                        }}
                      >
                        <FilledTriangle
                          size={isFocused ? 10 : 14}
                          color={selected ? INK : GREY_500}
                          selected={selected}
                          className="flex-shrink-0 translate-y-[0.5px] group-hover:translate-x-[2px] transition-transform duration-150"
                        />
                        <span>{section.title}</span>
                      </button>
                    </motion.li>
                  );
                })}
              </motion.ul>
            </nav>
          </motion.aside>

          <AnimatePresence>
            {isFocused && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3, ease: "easeOut" }}
                className="mt-auto"
              >
                <Image
                  src="/Womanlearning.svg"
                  alt="A woman engaged in learning."
                  width={300}
                  height={230}
                />
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <main
          className={`min-h-[45vh] ${
            isFocused ? "block" : "hidden"
          } min-w-0 w-full`}
          aria-live="polite"
        >
          <AnimatePresence mode="wait">
            {!activeSection ? (
              <motion.div
                key="hint"
                id="deep-dive-hint"
                initial={{ opacity: 0, y: 6 }}
                animate={{ opacity: 0.6, y: 0 }}
                exit={{ opacity: 0, y: -6 }}
                transition={{ duration: 0.2 }}
                className="text-sm"
                style={{ color: GREY_500 }}
              >
                Select a topic on the left to dive in.
              </motion.div>
            ) : (
              <motion.article
                key={activeSection.key}
                id={`deep-dive-panel-${activeSection.key}`}
                role="tabpanel"
                initial={{ opacity: 0, y: 12 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -8 }}
                transition={contentTransition}
                className="max-w-[1000px] mx-auto px-2"
                aria-labelledby={`deep-dive-title-${activeSection.key}`}
              >
                <h2
                  id={`deep-dive-title-${activeSection.key}`}
                  className="text-[28px] md:text-[32px] font-semibold leading-tight mb-3"
                  style={{
                    letterSpacing: "-0.02em",
                    color: INK,
                  }}
                >
                  {activeSection.title}
                </h2>

                {activeSection.summary && (
                  <p
                    className="mt-4 text-[15px] md:text-[16px] leading-relaxed"
                    style={{
                      color: GREY_700,
                      lineHeight: "1.6",
                    }}
                  >
                    {renderWithEmphasis(activeSection.summary, emphasisRuntime)}
                  </p>
                )}

                {activeSection.highlights?.length ? (
                  <div className="mt-6 space-y-3">
                    {activeSection.highlights.map((hl, idx) => (
                      <HighlightBlock
                        key={idx}
                        title={hl.title}
                        textNode={renderWithEmphasis(hl.text, emphasisRuntime)}
                      />
                    ))}
                  </div>
                ) : null}

                {activeSection.bullets?.length ? (
                  <ul
                    className="mt-6 space-y-2.5 text-[15px] md:text-[16px]"
                    style={{
                      color: INK,
                      lineHeight: "1.6",
                    }}
                  >
                    {activeSection.bullets.map((b, i) => (
                      <li key={i} className="flex gap-3">
                        <span
                          style={{
                            color: GREY_500,
                            fontSize: "8px",
                            marginTop: "9px",
                            flexShrink: 0,
                          }}
                        >
                          ●
                        </span>
                        <span>{renderWithEmphasis(b, emphasisRuntime)}</span>
                      </li>
                    ))}
                  </ul>
                ) : null}

                {activeSection.examples?.length ? (
                  <div className="mt-10 overflow-x-auto min-w-0">
                    <div
                      className="rounded-lg overflow-hidden border"
                      style={{ borderColor: GREY_200 }}
                    >
                      <table className="w-full border-collapse">
                        <thead
                          className="text-xs font-medium tracking-wide"
                          style={{
                            background: GREY_50,
                            color: GREY_700,
                          }}
                        >
                          <tr>
                            <th className="py-2.5 px-4 text-left font-medium">
                              Letter
                            </th>
                            <th
                              className="py-2.5 px-4 text-left border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Isolated
                            </th>
                            <th
                              className="py-2.5 px-4 text-left border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Initial
                            </th>
                            <th
                              className="py-2.5 px-4 text-left border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Medial
                            </th>
                            <th
                              className="py-2.5 px-4 text-left border-l font-medium"
                              style={{ borderColor: GREY_200 }}
                            >
                              Final
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {activeSection.examples.map((ex, i) => (
                            <motion.tr
                              key={i}
                              className="border-t transition-colors duration-150"
                              style={{
                                borderColor: GREY_200,
                              }}
                              whileHover={{
                                backgroundColor: GREY_50,
                              }}
                            >
                              <td
                                className="py-3 px-4 font-medium text-sm"
                                style={{ color: INK }}
                              >
                                {ex.label}
                              </td>
                              <HoverLetterCell text={ex.isolated} withDivider />
                              <HoverLetterCell text={ex.initial} withDivider />
                              <HoverLetterCell text={ex.medial} withDivider />
                              <HoverLetterCell text={ex.final} withDivider />
                            </motion.tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : null}

                {activeSection.tables?.length ? (
                  <div className="mt-10 space-y-8">
                    {activeSection.tables.map((t, i) => (
                      <DataTable
                        key={i}
                        table={t}
                        renderEmphasis={(s) =>
                          renderWithEmphasis(s, emphasisRuntime)
                        }
                      />
                    ))}
                  </div>
                ) : null}
              </motion.article>
            )}
          </AnimatePresence>
        </main>
      </div>
    </section>
  );
}

function HeaderBadge({
  number,
  compact = false,
}: {
  number: number;
  compact?: boolean;
}) {
  return (
    <div className="flex items-start gap-3.5 select-none">
      <div
        className={`${
          compact ? "text-[64px]" : "text-[80px]"
        } font-bold leading-[0.9] tabular-nums`}
        style={{
          letterSpacing: "-0.03em",
          color: INK,
        }}
      >
        {String(number).padStart(2, "0")}
      </div>
      <div
        className="text-[16px] leading-snug font-medium"
        style={{
          color: GREY_700,
          letterSpacing: "0.01em",
          marginTop: "4px",
        }}
      >
        <div>Dive</div>
        <div>Deeper</div>
      </div>
    </div>
  );
}

function HoverLetterCell({
  text,
  withDivider = false,
}: {
  text: string;
  withDivider?: boolean;
}) {
  return (
    <td
      className={`py-3 px-4 ${withDivider ? "border-l" : ""}`}
      style={withDivider ? { borderColor: GREY_200 } : undefined}
    >
      <motion.span
        className="inline-block font-arabic text-[26px] cursor-pointer select-none rounded px-1"
        style={{
          color: INK,
          transformOrigin: "center",
        }}
        whileHover={{
          x: 4,
          scale: 1.08,
          color: INK,
        }}
        transition={{
          duration: 0.15,
          ease: "easeOut",
        }}
        dir="auto"
      >
        {text}
      </motion.span>
    </td>
  );
}

function HighlightBlock({
  title,
  textNode,
}: {
  title: string;
  textNode: React.ReactNode;
}) {
  return (
    <motion.div
      role="note"
      dir="auto"
      className="w-full rounded-lg px-6 md:px-8 py-6 md:py-7 select-text whitespace-pre-wrap border transition-colors duration-200 cursor-default"
      style={{
        background: GREY_50,
        borderColor: GREY_200,
        color: INK,
        lineHeight: "1.7",
      }}
      whileHover={{
        backgroundColor: GREY_100,
        borderColor: GREY_300,
      }}
    >
      <div
        className="text-[11px] font-medium uppercase tracking-wider mb-3"
        style={{
          color: GREY_700,
          letterSpacing: "0.08em",
        }}
      >
        {title}
      </div>
      <span className="block text-[20px] md:text-[22px] font-semibold font-arabic">
        {textNode}
      </span>
    </motion.div>
  );
}

function DataTable({
  table,
  renderEmphasis,
}: {
  table: DeepDiveTable;
  renderEmphasis?: (s: string) => React.ReactNode;
}) {
  const isRTL = React.useCallback(
    (colIdx: number) => !!table.rtlColumns?.includes(colIdx),
    [table.rtlColumns]
  );

  return (
    <div className="overflow-x-auto min-w-0">
      {table.title ? (
        <h3
          className="mb-4 text-[18px] font-semibold"
          style={{ color: INK, letterSpacing: "-0.01em" }}
        >
          {table.title}
        </h3>
      ) : null}

      <div
        className="rounded-lg overflow-hidden border"
        style={{ borderColor: GREY_200 }}
      >
        <table className="w-full border-collapse">
          <thead
            className="text-xs font-medium tracking-wide"
            style={{
              background: GREY_50,
              color: GREY_700,
            }}
          >
            <tr>
              {table.headers.map((h, i) => (
                <th
                  key={i}
                  scope="col"
                  className={`py-2.5 px-4 text-left font-medium ${
                    i > 0 ? "border-l" : ""
                  }`}
                  style={i > 0 ? { borderColor: GREY_200 } : undefined}
                  dir={isRTL(i) ? "rtl" : "auto"}
                >
                  {h}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {table.rows.map((row, rIdx) => (
              <motion.tr
                key={rIdx}
                className="border-t transition-colors duration-150"
                style={{ borderColor: GREY_200 }}
                whileHover={{
                  backgroundColor: GREY_50,
                }}
              >
                {row.map((cell, cIdx) => (
                  <td
                    key={cIdx}
                    className={`py-2.5 px-4 align-top text-[14px] ${
                      cIdx > 0 ? "border-l" : ""
                    } ${isRTL(cIdx) ? "font-arabic text-[16px]" : ""}`}
                    style={{
                      borderColor: cIdx > 0 ? GREY_200 : undefined,
                      color: INK,
                      lineHeight: "1.5",
                    }}
                    dir={isRTL(cIdx) ? "rtl" : "auto"}
                  >
                    {renderEmphasis ? renderEmphasis(cell) : cell}
                  </td>
                ))}
              </motion.tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
