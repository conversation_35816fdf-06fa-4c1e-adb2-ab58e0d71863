// app/deepDive/content.ts

export type DeepDiveExample = {
  label: string; // e.g., "ب"
  isolated: string;
  initial: string;
  medial: string;
  final: string;
};

export type DeepDiveTable = {
  title?: string;
  headers: string[];
  rows: string[][];
  /** 0-based column indexes that should render RTL (e.g., Arabic columns) */
  rtlColumns?: number[];
};

export type DeepDiveHighlight = {
  /** Short 1-2 word label describing the content (e.g., "Non-joining letters", "Full alphabet") */
  title: string;
  /** The actual Arabic text or content to display */
  text: string;
};

/** NEW: Controls for how term matching should behave */
export type DeepDiveEmphasisOptions = {
  /** Match only whole words (recommended for Latin text; for Arabic, implementation should still respect letter boundaries) */
  wholeWord?: boolean;
  /** Case sensitivity for Latin scripts */
  caseSensitive?: boolean;
  /** If false, your renderer can normalize/strip harakat for Arabic matching while preserving original display */
  respectDiacritics?: boolean;
};

/** NEW: Declarative emphasis per section */
export type DeepDiveEmphasis = {
  /** Words/phrases to render in <strong> */
  bold?: string[];
  /** Words/phrases to render in <em> */
  italic?: string[];
  /** Optional matching behavior */
  options?: DeepDiveEmphasisOptions;
};

export type DeepDiveSection = {
  key: string;
  title: string;
  summary: string;
  bullets?: string[];
  examples?: DeepDiveExample[];
  /** Quick-reference grey blocks with descriptive titles */
  highlights?: DeepDiveHighlight[];
  /** Markdown-style data tables (e.g., makharij) */
  tables?: DeepDiveTable[];
  /** NEW: Section-level emphasis metadata for bold/italics */
  emphasis?: DeepDiveEmphasis;
};

export type DeepDiveLesson = {
  lessonNumber: number;
  sections: DeepDiveSection[];
};

export const deepDiveContent: Record<number, DeepDiveLesson> = {
  1: {
    lessonNumber: 1,
    sections: [
      {
        key: "shapes-by-position",
        title: "Letter Shapes by Position",
        summary:
          "Arabic letters connect differently depending on their position within a word. Many letters have four contextual forms: isolated, initial, medial, and final.",
        bullets: [
          "Some letters do not connect to the letter after them.",
          "Practice reading left-to-right in this table, but remember Arabic is written right-to-left.",
          "Focus on the baseline and connection strokes; the core letter often remains recognizable.",
        ],
        highlights: [
          {
            title: "Non-joining letters",
            text: "ا د ذ ر ز و",
          },
          {
            title: "Full alphabet",
            text: "ا ب ت ث ج ح خ د ذ ر ز س ش ص ض ط ظ ع غ ف ق ك ل م ن ه و ي",
          },
        ],
        examples: [
          {
            label: "ب",
            isolated: "ب",
            initial: "بـ",
            medial: "ـبـ",
            final: "ـب",
          },
          {
            label: "م",
            isolated: "م",
            initial: "مـ",
            medial: "ـمـ",
            final: "ـم",
          },
          {
            label: "ع",
            isolated: "ع",
            initial: "عـ",
            medial: "ـعـ",
            final: "ـع",
          },
        ],
        /** NEW: Emphasis for this section */
        emphasis: {
          bold: [
            // English key terms
            "isolated",
            "initial",
            "medial",
            "final",
            "connect",
            "contextual forms",
            "right-to-left",
            "baseline",
            "connection strokes",
            // Arabic keywords/letters
            "الحروف",
            "اتصال",
            "ا",
            "د",
            "ذ",
            "ر",
            "ز",
            "و",
          ],
          italic: ["non-joining", "core letter", "table", "word"],
          options: {
            wholeWord: true,
            caseSensitive: false,
            respectDiacritics: false,
          },
        },
      },
      {
        key: "makharij",
        title: "Makharij - Points of Articulation",
        summary:
          "Each letter is produced from a specific point in the mouth or throat. Knowing the makharij sharpens clarity and prevents common mistakes.",
        bullets: [
          "Throat (Halq) letters: ء ه (upper), ع ح (middle), غ خ (lower).",
          "Tongue groups: include sounds formed at the tip, blade, and back of the tongue (e.g., ت د ط near the alveolar region; ق at the back).",
          "Lips: ب م (lip closure), و (rounded lips without full closure).",
          "Nasal resonance (Ghunnah) primarily in ن and م when doubled or under specific rules.",
        ],
        tables: [
          {
            title: "Selected Makharij Examples",
            headers: [
              "Letter(s)",
              "Exit (Arabic)",
              "Exit (English)",
              "Common Learner Error",
            ],
            rows: [
              [
                "ا",
                "أقصى الحلق",
                "furthest throat",
                "glottal stop vs. prolongation",
              ],
              [
                "ق ك",
                "أقصى اللسان مع ما يحاذيه من الحنك الأعلى",
                "tongue's extreme back + soft palate",
                "confusing ق with ك",
              ],
              [
                "ش ج ي",
                "أوسط اللسان مع ما يحاذيه من الحنك الأعلى",
                "mid-tongue + hard palate",
                "over-curving tongue → distortion",
              ],
            ],
            rtlColumns: [0, 1],
          },
        ],
        /** NEW: Emphasis for this section */
        emphasis: {
          bold: [
            "Makharij",
            "Points of Articulation",
            "throat",
            "Halq",
            "tongue",
            "lips",
            "Ghunnah",
            // Arabic anchors
            "مخارج",
            "الحلق",
            "اللسان",
            "الشفتان",
            "غنة",
            "أقصى الحلق",
            "أوسط اللسان",
            "الحنك",
            "ق",
            "ك",
            "ش",
            "ج",
            "ي",
            "ء",
            "ه",
            "ع",
            "ح",
            "غ",
            "خ",
            "ب",
            "م",
            "و",
            "ن",
          ],
          italic: [
            "clarity",
            "common mistakes",
            "alveolar",
            "soft palate",
            "hard palate",
            "prolongation",
          ],
          options: {
            wholeWord: true,
            caseSensitive: false,
            respectDiacritics: false,
          },
        },
      },
      {
        key: "heavy-vs-light",
        title: "Heavy vs Light (Tafkhīm vs Tarqīq)",
        summary:
          "Some letters are pronounced with a full, heavy quality (tafkhīm), while others are light (tarqīq). Mastering this contrast is essential for accurate recitation.",
        bullets: [
          "ر can be heavy or light depending on surrounding vowels and rules; ل in لفظ الجلالة (Allah) is often heavy after fatḥah or ḍammah.",
          "ا reflects the quality of the preceding letter (itself doesn't carry heaviness).",
          "Listen carefully to vowel coloring: heavy letters 'spread' the vowel; light letters keep it thin.",
        ],
        highlights: [
          {
            title: "Always heavy",
            text: "خ ص ض غ ط ق ظ",
          },
        ],
        /** NEW: Emphasis for this section */
        emphasis: {
          bold: [
            "Heavy",
            "Light",
            "Tafkhīm",
            "Tarqīq",
            "لفظ الجلالة",
            "ر",
            "ل",
            "ا",
            "fatḥah",
            "ḍammah",
            "خ",
            "ص",
            "ض",
            "غ",
            "ط",
            "ق",
            "ظ",
          ],
          italic: [
            "vowel coloring",
            "spread",
            "thin",
            "accurate recitation",
            "surrounding vowels",
          ],
          options: {
            wholeWord: true,
            caseSensitive: false,
            respectDiacritics: false,
          },
        },
      },
    ],
  },
};
